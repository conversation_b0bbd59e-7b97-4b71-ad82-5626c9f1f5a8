import os
import random
import numpy as np
import matplotlib.pyplot as plt
from colon_cancer_classifier import ColonCancerClassifier
import cv2

def test_random_samples():
    """
    测试随机样本
    """
    print("=== 测试随机样本 ===")
    
    # 创建分类器实例
    classifier = ColonCancerClassifier()
    
    # 尝试加载已训练的模型
    if not classifier.load_model('colon_cancer_classifier.h5'):
        if not classifier.load_model('best_colon_cancer_model.h5'):
            print("未找到训练好的模型，请先运行训练脚本")
            return
    
    # 获取测试图片路径
    aca_dir = os.path.join('colon_subset', 'colon_aca')
    normal_dir = os.path.join('colon_subset', 'colon_n')
    
    # 随机选择一些图片进行测试
    aca_files = [f for f in os.listdir(aca_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    normal_files = [f for f in os.listdir(normal_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    # 随机选择测试样本
    test_samples = []
    
    # 选择5个癌细胞样本
    for i in range(5):
        if aca_files:
            filename = random.choice(aca_files)
            test_samples.append((os.path.join(aca_dir, filename), '癌细胞', 0))
    
    # 选择5个良性样本
    for i in range(5):
        if normal_files:
            filename = random.choice(normal_files)
            test_samples.append((os.path.join(normal_dir, filename), '良性细胞', 1))
    
    # 测试样本
    correct_predictions = 0
    total_predictions = len(test_samples)
    
    print(f"测试 {total_predictions} 个样本...")
    
    # 创建图像网格显示结果
    fig, axes = plt.subplots(2, 5, figsize=(20, 8))
    axes = axes.flatten()
    
    for i, (image_path, true_label, true_class) in enumerate(test_samples):
        try:
            # 预测
            predicted_class, confidence, predicted_label = classifier.predict_single_image(image_path)
            
            if predicted_class is not None:
                # 检查预测是否正确
                is_correct = predicted_class == true_class
                if is_correct:
                    correct_predictions += 1
                
                # 加载图像用于显示
                img = cv2.imread(image_path)
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                
                # 显示图像
                axes[i].imshow(img)
                
                # 设置标题颜色
                title_color = 'green' if is_correct else 'red'
                axes[i].set_title(
                    f'真实: {true_label}\n预测: {predicted_label}\n置信度: {confidence:.3f}',
                    color=title_color,
                    fontsize=10
                )
                axes[i].axis('off')
                
                print(f"样本 {i+1}: 真实={true_label}, 预测={predicted_label}, "
                      f"置信度={confidence:.3f}, 正确={is_correct}")
            else:
                print(f"样本 {i+1}: 预测失败")
                
        except Exception as e:
            print(f"样本 {i+1} 处理失败: {e}")
    
    # 计算准确率
    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    print(f"\n测试结果:")
    print(f"总样本数: {total_predictions}")
    print(f"正确预测: {correct_predictions}")
    print(f"准确率: {accuracy:.2%}")
    
    plt.suptitle(f'随机样本测试结果 - 准确率: {accuracy:.2%}', fontsize=16)
    plt.tight_layout()
    plt.savefig('test_results.png', dpi=300, bbox_inches='tight')
    plt.show()


def test_specific_image(image_path):
    """
    测试特定图像
    """
    print(f"=== 测试特定图像: {image_path} ===")
    
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    # 创建分类器实例
    classifier = ColonCancerClassifier()
    
    # 尝试加载已训练的模型
    if not classifier.load_model('colon_cancer_classifier.h5'):
        if not classifier.load_model('best_colon_cancer_model.h5'):
            print("未找到训练好的模型，请先运行训练脚本")
            return
    
    # 预测
    predicted_class, confidence, predicted_label = classifier.predict_single_image(image_path)
    
    if predicted_class is not None:
        print(f"预测结果: {predicted_label}")
        print(f"置信度: {confidence:.4f}")
        
        # 给出建议
        if predicted_class == 0:  # 癌细胞
            print("⚠️ 检测到癌细胞特征，建议进一步医学检查")
        else:  # 良性
            print("✅ 检测到良性细胞特征")
    else:
        print("预测失败")


def show_data_distribution():
    """
    显示数据分布
    """
    print("=== 数据分布统计 ===")
    
    aca_dir = os.path.join('colon_subset', 'colon_aca')
    normal_dir = os.path.join('colon_subset', 'colon_n')
    
    # 统计文件数量
    aca_count = len([f for f in os.listdir(aca_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    normal_count = len([f for f in os.listdir(normal_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    
    total_count = aca_count + normal_count
    
    print(f"癌细胞图像: {aca_count} ({aca_count/total_count:.1%})")
    print(f"良性细胞图像: {normal_count} ({normal_count/total_count:.1%})")
    print(f"总图像数: {total_count}")
    
    # 绘制分布图
    labels = ['癌细胞', '良性细胞']
    sizes = [aca_count, normal_count]
    colors = ['#ff6b6b', '#4ecdc4']
    
    plt.figure(figsize=(10, 6))
    
    # 饼图
    plt.subplot(1, 2, 1)
    plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title('数据分布')
    
    # 柱状图
    plt.subplot(1, 2, 2)
    bars = plt.bar(labels, sizes, color=colors)
    plt.title('样本数量')
    plt.ylabel('图像数量')
    
    # 在柱状图上添加数值
    for bar, size in zip(bars, sizes):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                str(size), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('data_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()


def main():
    """
    主测试函数
    """
    print("结肠癌细胞分类器测试程序")
    print("=" * 50)
    
    while True:
        print("\n请选择测试选项:")
        print("1. 显示数据分布")
        print("2. 测试随机样本")
        print("3. 测试特定图像")
        print("4. 退出")
        
        choice = input("\n请输入选项 (1-4): ").strip()
        
        if choice == '1':
            show_data_distribution()
        elif choice == '2':
            test_random_samples()
        elif choice == '3':
            image_path = input("请输入图像路径: ").strip()
            test_specific_image(image_path)
        elif choice == '4':
            print("退出测试程序")
            break
        else:
            print("无效选项，请重新选择")


if __name__ == "__main__":
    main()
