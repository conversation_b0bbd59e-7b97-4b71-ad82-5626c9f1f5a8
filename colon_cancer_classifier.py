import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.preprocessing.image import ImageDataGenerator, load_img, img_to_array
from tensorflow.keras.utils import to_categorical
import cv2
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

class ColonCancerClassifier:
    def __init__(self, data_dir='colon_subset', img_size=(224, 224), batch_size=32):
        """
        初始化结肠癌分类器
        
        Args:
            data_dir: 数据集目录
            img_size: 图像尺寸
            batch_size: 批次大小
        """
        self.data_dir = data_dir
        self.img_size = img_size
        self.batch_size = batch_size
        self.model = None
        self.history = None
        self.class_names = ['colon_aca', 'colon_n']  # 0: 癌细胞, 1: 良性
        
    def load_and_preprocess_data(self):
        """
        加载和预处理数据
        """
        print("正在加载数据...")
        
        # 存储图像和标签
        images = []
        labels = []
        
        # 加载癌细胞图像 (标签: 0)
        aca_dir = os.path.join(self.data_dir, 'colon_aca')
        for filename in os.listdir(aca_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(aca_dir, filename)
                try:
                    # 加载和预处理图像
                    img = cv2.imread(img_path)
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    img = cv2.resize(img, self.img_size)
                    img = img.astype('float32') / 255.0
                    
                    images.append(img)
                    labels.append(0)  # 癌细胞
                except Exception as e:
                    print(f"加载图像失败 {img_path}: {e}")
        
        # 加载良性细胞图像 (标签: 1)
        normal_dir = os.path.join(self.data_dir, 'colon_n')
        for filename in os.listdir(normal_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(normal_dir, filename)
                try:
                    # 加载和预处理图像
                    img = cv2.imread(img_path)
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    img = cv2.resize(img, self.img_size)
                    img = img.astype('float32') / 255.0
                    
                    images.append(img)
                    labels.append(1)  # 良性
                except Exception as e:
                    print(f"加载图像失败 {img_path}: {e}")
        
        # 转换为numpy数组
        self.X = np.array(images)
        self.y = np.array(labels)
        
        print(f"数据加载完成!")
        print(f"总图像数量: {len(self.X)}")
        print(f"癌细胞图像: {np.sum(self.y == 0)}")
        print(f"良性细胞图像: {np.sum(self.y == 1)}")
        print(f"图像形状: {self.X.shape}")
        
        return self.X, self.y
    
    def split_data(self, test_size=0.2, val_size=0.2, random_state=42):
        """
        划分训练集、验证集和测试集
        """
        print("正在划分数据集...")
        
        # 首先划分训练集和测试集
        X_temp, self.X_test, y_temp, self.y_test = train_test_split(
            self.X, self.y, test_size=test_size, random_state=random_state, stratify=self.y
        )
        
        # 再从临时数据中划分训练集和验证集
        val_size_adjusted = val_size / (1 - test_size)
        self.X_train, self.X_val, self.y_train, self.y_val = train_test_split(
            X_temp, y_temp, test_size=val_size_adjusted, random_state=random_state, stratify=y_temp
        )
        
        # 转换标签为分类格式
        self.y_train_cat = to_categorical(self.y_train, 2)
        self.y_val_cat = to_categorical(self.y_val, 2)
        self.y_test_cat = to_categorical(self.y_test, 2)
        
        print(f"训练集: {self.X_train.shape[0]} 样本")
        print(f"验证集: {self.X_val.shape[0]} 样本")
        print(f"测试集: {self.X_test.shape[0]} 样本")
        
        return self.X_train, self.X_val, self.X_test, self.y_train, self.y_val, self.y_test
    
    def create_data_generators(self):
        """
        创建数据增强生成器
        """
        # 训练数据增强
        train_datagen = ImageDataGenerator(
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            horizontal_flip=True,
            vertical_flip=True,
            zoom_range=0.2,
            shear_range=0.2,
            fill_mode='nearest'
        )
        
        # 验证和测试数据不进行增强
        val_test_datagen = ImageDataGenerator()
        
        self.train_generator = train_datagen.flow(
            self.X_train, self.y_train_cat, batch_size=self.batch_size
        )
        
        self.val_generator = val_test_datagen.flow(
            self.X_val, self.y_val_cat, batch_size=self.batch_size
        )
        
        return self.train_generator, self.val_generator
    
    def build_cnn_model(self):
        """
        构建CNN模型
        """
        print("正在构建CNN模型...")
        
        model = keras.Sequential([
            # 第一个卷积块
            layers.Conv2D(32, (3, 3), activation='relu', input_shape=(*self.img_size, 3)),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # 第二个卷积块
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # 第三个卷积块
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # 第四个卷积块
            layers.Conv2D(256, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # 全连接层
            layers.Flatten(),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            # 输出层
            layers.Dense(2, activation='softmax')
        ])
        
        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        self.model = model
        
        print("模型构建完成!")
        print(model.summary())
        
        return model
    
    def train_model(self, epochs=50, patience=10):
        """
        训练模型
        """
        print("开始训练模型...")
        
        # 回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=patience,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'best_colon_cancer_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # 训练模型
        self.history = self.model.fit(
            self.train_generator,
            epochs=epochs,
            validation_data=self.val_generator,
            callbacks=callbacks,
            verbose=1
        )
        
        print("模型训练完成!")
        return self.history
    
    def evaluate_model(self):
        """
        评估模型性能
        """
        print("正在评估模型...")
        
        # 在测试集上预测
        test_predictions = self.model.predict(self.X_test)
        test_pred_classes = np.argmax(test_predictions, axis=1)
        
        # 计算准确率
        test_accuracy = accuracy_score(self.y_test, test_pred_classes)
        print(f"测试集准确率: {test_accuracy:.4f}")
        
        # 分类报告
        print("\n分类报告:")
        print(classification_report(self.y_test, test_pred_classes, 
                                  target_names=['癌细胞', '良性细胞']))
        
        # 混淆矩阵
        cm = confusion_matrix(self.y_test, test_pred_classes)
        
        # 绘制混淆矩阵
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['癌细胞', '良性细胞'],
                   yticklabels=['癌细胞', '良性细胞'])
        plt.title('混淆矩阵')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return test_accuracy, cm
    
    def plot_training_history(self):
        """
        绘制训练历史
        """
        if self.history is None:
            print("没有训练历史可绘制")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 绘制准确率
        ax1.plot(self.history.history['accuracy'], label='训练准确率')
        ax1.plot(self.history.history['val_accuracy'], label='验证准确率')
        ax1.set_title('模型准确率')
        ax1.set_xlabel('轮次')
        ax1.set_ylabel('准确率')
        ax1.legend()
        ax1.grid(True)
        
        # 绘制损失
        ax2.plot(self.history.history['loss'], label='训练损失')
        ax2.plot(self.history.history['val_loss'], label='验证损失')
        ax2.set_title('模型损失')
        ax2.set_xlabel('轮次')
        ax2.set_ylabel('损失')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def predict_single_image(self, image_path):
        """
        预测单张图像
        """
        try:
            # 加载和预处理图像
            img = cv2.imread(image_path)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img = cv2.resize(img, self.img_size)
            img = img.astype('float32') / 255.0
            img = np.expand_dims(img, axis=0)
            
            # 预测
            prediction = self.model.predict(img)
            predicted_class = np.argmax(prediction, axis=1)[0]
            confidence = np.max(prediction)
            
            # 结果解释
            class_name = '癌细胞' if predicted_class == 0 else '良性细胞'
            
            print(f"预测结果: {class_name}")
            print(f"置信度: {confidence:.4f}")
            
            # 显示图像和预测结果
            plt.figure(figsize=(8, 6))
            plt.imshow(cv2.imread(image_path)[:,:,::-1])
            plt.title(f'预测: {class_name} (置信度: {confidence:.4f})')
            plt.axis('off')
            plt.tight_layout()
            plt.show()
            
            return predicted_class, confidence, class_name
            
        except Exception as e:
            print(f"预测失败: {e}")
            return None, None, None
    
    def save_model(self, filepath='colon_cancer_classifier.h5'):
        """
        保存模型
        """
        if self.model is not None:
            self.model.save(filepath)
            print(f"模型已保存到: {filepath}")
        else:
            print("没有模型可保存")
    
    def load_model(self, filepath='colon_cancer_classifier.h5'):
        """
        加载模型
        """
        try:
            self.model = keras.models.load_model(filepath)
            print(f"模型已从 {filepath} 加载")
            return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False


def main():
    """
    主函数 - 完整的训练和测试流程
    """
    print("=== 结肠癌细胞分类器 ===")
    
    # 创建分类器实例
    classifier = ColonCancerClassifier()
    
    # 加载和预处理数据
    X, y = classifier.load_and_preprocess_data()
    
    # 划分数据集
    classifier.split_data()
    
    # 创建数据生成器
    classifier.create_data_generators()
    
    # 构建模型
    classifier.build_cnn_model()
    
    # 训练模型
    classifier.train_model(epochs=30)
    
    # 绘制训练历史
    classifier.plot_training_history()
    
    # 评估模型
    classifier.evaluate_model()
    
    # 保存模型
    classifier.save_model()
    
    print("\n=== 训练完成! ===")
    print("现在可以使用 predict_single_image() 方法来预测新图像")


if __name__ == "__main__":
    main()
